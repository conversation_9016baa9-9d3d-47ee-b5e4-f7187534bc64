# File Pane Refactoring - Consolidated Implementation Plan

## Overview

This document provides the final, authoritative implementation plan for refactoring the File Pane component in the Update Data module. It consolidates architectural decisions from the design documents and addresses all user feedback to provide a clear, phased approach ready for implementation.

## Architectural Foundation

### Target Architecture
- **Smart Widget Pattern**: Self-contained file view component with clean API
- **Clean MVP Separation**: Zero Qt coupling in presenter layer
- **Interface vs Events Separation**: Methods for direct operations, events for asynchronous communication
- **Minimal Change Approach**: Preserve existing structure while improving architecture

### Directory Structure

```
update_data/
├── ud_presenter.py              # TOP LEVEL - module coordinator/API
├── ud_view.py                   # TOP LEVEL - main view
├── interface/
│   └── i_view_interface.py      # EXISTS - needs editing for file operations
├── services/
│   ├── events.py                # EXISTS - centralized event publishing
│   └── events_data.py           # EXISTS - consider renaming to event_models.py
├── _view/                       # CURRENT UI CODE
│   ├── center_panel.py          # RENAME TO: center_panel_layout_manager.py
│   ├── left_panel.py            # RENAME TO: left_panel_layout_manager.py
│   ├── right_panel.py           # RENAME TO: right_panel_layout_manager.py
│   └── center_panel_components/
│       └── file_pane_v2/        # NEW COMPONENT FOLDER
│           ├── __init__.py
│           ├── ud_file_view.py  # MAIN COMPONENT
│           ├── components/      # INTERNAL WIDGETS
│           │   ├── __init__.py
│           │   ├── file_table.py
│           │   ├── add_remove_btns.py
│           │   └── context_menu.py
│           ├── models.py        # DATA STRUCTURES
│           ├── config.py        # CONFIGURATION
│           └── utils.py         # HELPER FUNCTIONS
```

## Implementation Phases

### Phase 1: Foundation Setup

#### 1.1 Directory Structure Creation
```bash
# Create new component folder
mkdir -p flatmate/src/fm/modules/update_data/_view/center_panel_components/file_pane_v2/components

# Create __init__.py files
touch flatmate/src/fm/modules/update_data/_view/center_panel_components/file_pane_v2/__init__.py
touch flatmate/src/fm/modules/update_data/_view/center_panel_components/file_pane_v2/components/__init__.py
```

#### 1.2 Rename Panel Files (Layout Managers)
- Rename `center_panel.py` → `center_panel_layout_manager.py`
- Rename `left_panel.py` → `left_panel_layout_manager.py`
- Rename `right_panel.py` → `right_panel_layout_manager.py`
- Update all imports throughout the codebase

#### 1.3 Events System Clarification
- **Examine existing**: `services/events.py` and `services/events_data.py`
- **Consider renaming**: `events_data.py` → `event_models.py` (clearer naming)
- **Define new events**: Add file-specific events to existing system

#### 1.4 Interface Extension
- **Edit existing**: `interface/i_view_interface.py`
- **Add file operations**: Methods for file management operations
- **Maintain compatibility**: Ensure existing interface methods remain functional

### Phase 2: Core Component Implementation

#### 2.1 Data Model Implementation
Create `_view/center_panel_components/file_pane_v2/models.py`:
```python
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

@dataclass
class FileInfo:
    """Data structure for file information."""
    path: str
    size: int
    modified: datetime
    file_type: str
    is_valid: bool = True
    is_processed: bool = False
    
class FileViewModel:
    """Data model for the file view component."""
    def __init__(self):
        self.files: List[FileInfo] = []
        self.selected_file: Optional[str] = None
```

#### 2.2 Configuration Implementation
Create `_view/center_panel_components/file_pane_v2/config.py`:
```python
from dataclasses import dataclass, field
from typing import List

@dataclass
class FileConfig:
    """Configuration for file view behaviour and appearance."""
    show_file_icons: bool = True
    show_file_size: bool = True
    show_file_type: bool = True
    group_by_folder: bool = True
    sort_by: str = "name"  # "name", "size", "type", "date"
    sort_order: str = "asc"  # "asc", "desc"
    allow_add: bool = True
    allow_remove: bool = True
    allow_context_menu: bool = True
    allowed_file_types: List[str] = field(
        default_factory=lambda: ["*.csv"]
    )
```

#### 2.3 Internal Components Implementation
- **FileTable**: Core file display widget (`components/file_table.py`)
- **AddRemoveButtons**: Add/Remove functionality (`components/add_remove_btns.py`)
- **ContextMenu**: Right-click menu (`components/context_menu.py`)

#### 2.4 Main Component Implementation
Create `_view/center_panel_components/file_pane_v2/ud_file_view.py`:
```python
from PySide6.QtWidgets import QVBoxLayout
from fm.gui.components.shared.base_pane import BasePane
from .models import FileViewModel
from .config import FileConfig

class UDFileView(BasePane):
    """Self-contained file display component."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._model = FileViewModel()
        self._config = FileConfig()
        self._init_layout()
        self._setup_ui()
        self._connect_signals()
        
    # Public API methods (interface implementation)
    def set_files(self, files: List[str]) -> 'UDFileView': ...
    def add_file(self, file_path: str) -> 'UDFileView': ...
    def remove_file(self, file_path: str) -> 'UDFileView': ...
    def get_files(self) -> List[str]: ...
    def get_selected_file(self) -> Optional[str]: ...
```

### Phase 3: Integration and Signal Handling

#### 3.1 Interface Extension
Edit existing `interface/i_view_interface.py` to add file operations:
```python
# Add to existing IUpdateDataView Protocol
def add_files(self, files: List[str]) -> None: ...
def remove_file(self, file_path: str) -> None: ...
def get_current_files(self) -> List[str]: ...
def get_selected_file(self) -> Optional[str]: ...
```

#### 3.2 Events Integration
Extend existing `services/events.py` or `services/event_models.py`:
```python
# Add file-specific events to existing system
class FileViewEvents(QObject):
    """Events published by file view widget"""
    file_paths_list_updated = Signal(list)  # List[str] of file paths
    file_selected = Signal(str)             # Selected file path
    processing_requested = Signal()         # User wants to process files
```

#### 3.3 View Layer Updates
Update `ud_view.py`:
- Connect file view events to view-level signals
- Implement interface methods that delegate to file view component
- Remove signal handling from layout manager panels

#### 3.4 Presenter Updates
Update `ud_presenter.py`:
- Connect to view-level signals (not widget signals)
- Use interface methods for file operations
- Remove direct Qt widget dependencies

### Phase 4: Migration and Testing

#### 4.1 Parallel Implementation
- Keep original file pane functional during development
- Implement new component alongside existing one
- Test both implementations in parallel

#### 4.2 Integration Testing
- Verify signal flow: Widget → View → Presenter
- Test file operations end-to-end
- Ensure all existing functionality works

#### 4.3 Migration Switch
- Update center panel layout manager to use new file view
- Verify all functionality in production code
- Address any issues that arise

#### 4.4 Cleanup
- Remove old file pane implementation
- Update remaining references
- Clean up unused imports and files

## Key Implementation Notes

### Addressing User Comments

1. **Interface File Exists**: Edit existing `interface/i_view_interface.py` rather than creating new
2. **Panel Renaming**: Rename panel files to `*_layout_manager.py` pattern for clarity
3. **Events System**: Examine and potentially rename `events_data.py` to `event_models.py`
4. **Folder Naming**: Use `file_pane_v2/` to keep original as reference

### Verification Against Codebase

- ✅ `interface/i_view_interface.py` exists (3452 bytes) - will be edited
- ✅ `services/events.py` and `services/events_data.py` exist - will be extended
- ✅ Current `_view/` structure with panels - will be renamed and refactored
- ✅ `ud_presenter.py` and `ud_view.py` at top level - will be updated

### Benefits

1. **Clean Separation**: UI concerns clearly separated from business logic
2. **Zero Qt Coupling**: Presenter has no Qt dependencies
3. **Maintainable Structure**: Related files grouped together
4. **Minimal Disruption**: Existing code continues during migration
5. **Testable Components**: Clean boundaries enable easier testing

## Success Criteria

- [ ] File view component is self-contained and follows smart widget pattern
- [ ] Presenter has zero Qt coupling
- [ ] Interface methods and events are clearly separated
- [ ] All existing functionality is preserved
- [ ] Code is more maintainable and testable
- [ ] User comments and feedback are fully addressed
