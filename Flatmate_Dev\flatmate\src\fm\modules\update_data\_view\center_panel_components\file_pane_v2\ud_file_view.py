"""
Main file view component for the Update Data module.

This is the smart widget that encapsulates all file-related functionality
following the clean MVP pattern with zero Qt coupling in the presenter.
"""

from PySide6.QtWidgets import QVBoxLayout, QFileDialog
from PySide6.QtCore import Signal, QPoint
from PySide6.QtGui import QContextMenuEvent
from typing import List, Optional
import os
from pathlib import Path

from fm.gui._shared_components.base.base_pane import BasePane
from .models import FileViewModel, FileInfo
from .config import FileConfig
from .utils import create_file_info, validate_file_paths
from .components.file_table import FileTable
from .components.add_remove_btns import AddRemoveButtons
from .components.context_menu import FileContextMenu
from fm.core.services.logger import log


class UDFileView(BasePane):
    """Self-contained file display component with smart widget pattern."""
    
    # High-level domain signals (not Qt widget signals)
    file_list_changed = Signal(list)  # List[str] of file paths
    file_selected = Signal(str)       # Selected file path
    processing_requested = Signal()   # User wants to process files
    
    def __init__(self, config: Optional[FileConfig] = None, parent=None):
        """Initialize the file view component."""
        super().__init__(parent)
        
        # Initialize models and configuration
        self._model = FileViewModel()
        self._config = config or FileConfig.default()
        
        # Initialize components
        self._file_table: Optional[FileTable] = None
        self._add_remove_btns: Optional[AddRemoveButtons] = None
        self._context_menu: Optional[FileContextMenu] = None
        
        # Set up the component
        self._init_layout()
        self._setup_ui()
        self._connect_signals()
        
        log.debug("UDFileView component initialized")
    
    def _init_layout(self) -> None:
        """Initialize the layout."""
        self._layout = QVBoxLayout(self)
        self._layout.setContentsMargins(5, 5, 5, 5)
        self._layout.setSpacing(5)
    
    def _setup_ui(self) -> None:
        """Set up the user interface components."""
        # Create file table
        self._file_table = FileTable(self._model, self._config, self)
        self._layout.addWidget(self._file_table)
        
        # Create add/remove buttons
        self._add_remove_btns = AddRemoveButtons(self._config, self)
        self._layout.addWidget(self._add_remove_btns)
        
        # Create context menu handler
        self._context_menu = FileContextMenu(self._config, self)
        
        # Enable context menu on table
        if self._config.allow_context_menu:
            from PySide6.QtCore import Qt
            self._file_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
            self._file_table.customContextMenuRequested.connect(self._show_context_menu)
    
    def _connect_signals(self) -> None:
        """Connect internal component signals."""
        if self._file_table:
            self._file_table.file_selected.connect(self._on_file_selected)
            self._file_table.file_double_clicked.connect(self._on_file_double_clicked)
        
        if self._add_remove_btns:
            self._add_remove_btns.add_files_requested.connect(self._on_add_files_requested)
            self._add_remove_btns.remove_file_requested.connect(self._on_remove_file_requested)
        
        if self._context_menu:
            self._context_menu.remove_file_requested.connect(self._remove_file_by_path)
            self._context_menu.open_file_location_requested.connect(self._open_file_location)
            self._context_menu.file_properties_requested.connect(self._show_file_properties)
    
    # Public API methods (interface implementation)
    def set_files(self, file_paths: List[str]) -> 'UDFileView':
        """Set the list of files. Returns self for method chaining."""
        try:
            # Validate file paths
            valid_paths = validate_file_paths(file_paths, self._config)
            
            # Clear existing files
            self._model.clear_files()
            
            # Add valid files
            for file_path in valid_paths:
                file_info = create_file_info(file_path)
                if file_info:
                    self._model.add_file(file_info)
            
            # Refresh UI
            self._refresh_ui()
            
            # Emit signal
            self.file_list_changed.emit(self._model.get_file_paths())
            
            log.debug(f"Set {len(valid_paths)} files in file view")
            
        except Exception as e:
            log.error(f"Error setting files: {e}")
        
        return self
    
    def add_file(self, file_path: str) -> 'UDFileView':
        """Add a single file. Returns self for method chaining."""
        try:
            if validate_file_paths([file_path], self._config):
                file_info = create_file_info(file_path)
                if file_info:
                    self._model.add_file(file_info)
                    self._refresh_ui()
                    self.file_list_changed.emit(self._model.get_file_paths())
                    log.debug(f"Added file: {file_path}")
        
        except Exception as e:
            log.error(f"Error adding file {file_path}: {e}")
        
        return self
    
    def remove_file(self, file_path: str) -> 'UDFileView':
        """Remove a file by path. Returns self for method chaining."""
        try:
            if self._model.remove_file(file_path):
                self._refresh_ui()
                self.file_list_changed.emit(self._model.get_file_paths())
                log.debug(f"Removed file: {file_path}")
        
        except Exception as e:
            log.error(f"Error removing file {file_path}: {e}")
        
        return self
    
    def get_files(self) -> List[str]:
        """Get list of all file paths."""
        return self._model.get_file_paths()
    
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file path."""
        return self._model.selected_file
    
    def clear_files(self) -> 'UDFileView':
        """Clear all files. Returns self for method chaining."""
        self._model.clear_files()
        self._refresh_ui()
        self.file_list_changed.emit([])
        log.debug("Cleared all files from file view")
        return self
    
    # Internal event handlers
    def _on_file_selected(self, file_path: str) -> None:
        """Handle file selection."""
        self.file_selected.emit(file_path)
        self._update_button_states()
    
    def _on_file_double_clicked(self, file_path: str) -> None:
        """Handle file double click - could trigger processing."""
        log.debug(f"File double-clicked: {file_path}")
        # For now, just emit processing requested
        self.processing_requested.emit()
    
    def _on_add_files_requested(self) -> None:
        """Handle add files request."""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
        
        # Set file filters based on configuration
        filters = []
        for pattern in self._config.allowed_file_types:
            if pattern.startswith("*."):
                ext = pattern[2:].upper()
                filters.append(f"{ext} files ({pattern})")
        
        if filters:
            file_dialog.setNameFilters(filters)
        
        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            for file_path in selected_files:
                self.add_file(file_path)
    
    def _on_remove_file_requested(self) -> None:
        """Handle remove file request."""
        selected_file = self.get_selected_file()
        if selected_file:
            self.remove_file(selected_file)
    
    def _remove_file_by_path(self, file_path: str) -> None:
        """Remove file by path (used by context menu)."""
        self.remove_file(file_path)
    
    def _show_context_menu(self, position: QPoint) -> None:
        """Show context menu at the given position."""
        if not self._config.allow_context_menu or not self._context_menu:
            return
        
        # Get file at position
        item = self._file_table.itemAt(position)
        file_path = None
        if item:
            file_path = item.data(0)  # Qt.ItemDataRole.UserRole
        
        # Create and show menu
        menu = self._context_menu.create_menu(file_path)
        if menu.actions():
            menu.exec(self._file_table.mapToGlobal(position))
    
    def _open_file_location(self, file_path: str) -> None:
        """Open the folder containing the file."""
        try:
            folder_path = str(Path(file_path).parent)
            os.startfile(folder_path)  # Windows-specific
            log.debug(f"Opened file location: {folder_path}")
        except Exception as e:
            log.error(f"Error opening file location: {e}")
    
    def _show_file_properties(self, file_path: str) -> None:
        """Show file properties (placeholder for future implementation)."""
        log.debug(f"File properties requested for: {file_path}")
        # TODO: Implement file properties dialog
    
    def _refresh_ui(self) -> None:
        """Refresh the user interface."""
        if self._file_table:
            self._file_table.refresh_data()
        self._update_button_states()
    
    def _update_button_states(self) -> None:
        """Update button states based on current state."""
        if self._add_remove_btns:
            has_files = len(self._model.files) > 0
            has_selection = self._model.selected_file is not None
            self._add_remove_btns.update_button_states(has_files, has_selection)
    
    # BasePane overrides
    def clear(self) -> None:
        """Clear any data or selections in this pane."""
        self.clear_files()
