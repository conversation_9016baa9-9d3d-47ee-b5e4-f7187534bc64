#!/usr/bin/env python3
"""
Dedicated information widget for Update Data module.
Provides status, progress, and error reporting capabilities.
"""

from PySide6.QtWidgets import QLabel, QProgressBar, QVBoxLayout, QWidget


class UpdateDataInfoWidget(QWidget):
    """
    A comprehensive information widget for Update Data module.
    
    Provides:
    - Status messages
    - Progress tracking
    - Error/warning display
    """
    
    def __init__(self, parent=None):
        """Initialize the info widget."""
        super().__init__(parent)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Status message label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("""
            font-weight: bold;
            color: #FFFFFF;
        """)
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m files (%p%)")
        self.progress_bar.hide()  # Initially hidden
        layout.addWidget(self.progress_bar)
        
        # Error/warning label
        self.error_label = QLabel()
        self.error_label.setStyleSheet("""
            color: red;
            font-style: italic;
        """)
        self.error_label.hide()  # Initially hidden
        layout.addWidget(self.error_label)
        
        # Spacer to push everything to the top
        layout.addStretch(1)
    
    def set_status(self, message: str, is_error: bool = False):
        """
        Set the status message.
        
        Args:
            message: Status text to display
            is_error: If True, displays in red
        """
        self.status_label.setText(message)
        self.status_label.setStyleSheet(
            """
            font-weight: bold;
            color: red;
            """ if is_error else 
            """
            font-weight: bold;
            color: #FFFFFF;
            """
        )
    
    def set_progress(self, current: int, total: int):
        """
        Update progress bar.
        
        Args:
            current: Number of processed files
            total: Total number of files
        """
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
        self.progress_bar.show()
    
    def set_error(self, message: str):
        """
        Display an error message.
        
        Args:
            message: Error text to display
        """
        self.error_label.setText(message)
        self.error_label.show()
    
    def clear(self):
        """Reset all widgets to initial state."""
        self.status_label.setText("Ready")
        self.status_label.setStyleSheet("""
            font-weight: bold;
            color: #FFFFFF;
        """)
        self.progress_bar.hide()
        self.error_label.hide()
