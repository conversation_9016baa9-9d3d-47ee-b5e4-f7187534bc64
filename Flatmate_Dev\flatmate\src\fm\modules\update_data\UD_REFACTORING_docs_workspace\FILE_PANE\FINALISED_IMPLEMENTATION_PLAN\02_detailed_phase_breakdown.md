# Detailed Phase Breakdown - File Pane Refactoring

## Phase 1: Foundation Setup

### 1.1 Directory Structure Creation

**Objective**: Set up the new component folder structure while preserving existing code.

**Actions**:
```bash
# Navigate to update_data module
cd flatmate/src/fm/modules/update_data

# Create new component folder structure
mkdir -p _view/center_panel_components/file_pane_v2/components

# Create __init__.py files
touch _view/center_panel_components/file_pane_v2/__init__.py
touch _view/center_panel_components/file_pane_v2/components/__init__.py
```

**Files Created**:
- `_view/center_panel_components/file_pane_v2/__init__.py`
- `_view/center_panel_components/file_pane_v2/components/__init__.py`

### 1.2 Panel File Renaming

**Objective**: Rename panel files to clarify their role as layout managers.

**Actions**:
1. Rename files:
   - `_view/center_panel.py` → `_view/center_panel_layout_manager.py`
   - `_view/left_panel.py` → `_view/left_panel_layout_manager.py`
   - `_view/right_panel.py` → `_view/right_panel_layout_manager.py`

2. Update imports in:
   - `ud_view.py`
   - Any other files importing these panels

**Files Modified**:
- `_view/center_panel.py` (renamed)
- `_view/left_panel.py` (renamed)
- `_view/right_panel.py` (renamed)
- `ud_view.py` (imports updated)

### 1.3 Events System Review and Enhancement

**Objective**: Examine existing events system and enhance for file operations.

**Current Files to Examine**:
- `services/events.py` (3KB) - centralized event publishing
- `services/events_data.py` (unknown size) - event data structures

**Actions**:
1. Review existing event structure
2. Consider renaming `events_data.py` → `event_models.py` (clearer naming)
3. Add file-specific events to existing system
4. Ensure compatibility with current event bus usage

**Potential Files Modified**:
- `services/events_data.py` (possibly renamed to `event_models.py`)
- `services/events.py` (enhanced with file events)

### 1.4 Interface Extension

**Objective**: Extend existing interface for file operations.

**Current File**: `interface/i_view_interface.py` (3452 bytes)

**Actions**:
1. Review current interface methods
2. Add file operation methods:
   ```python
   def add_files(self, files: List[str]) -> None: ...
   def remove_file(self, file_path: str) -> None: ...
   def get_current_files(self) -> List[str]: ...
   def get_selected_file(self) -> Optional[str]: ...
   ```
3. Ensure backward compatibility

**Files Modified**:
- `interface/i_view_interface.py`

## Phase 2: Core Component Implementation

### 2.1 Data Model Implementation

**Objective**: Create data structures for file view component.

**File**: `_view/center_panel_components/file_pane_v2/models.py`

**Implementation**:
```python
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

@dataclass
class FileInfo:
    """Data structure for file information."""
    path: str
    size: int
    modified: datetime
    file_type: str
    is_valid: bool = True
    is_processed: bool = False
    
class FileViewModel:
    """Data model for the file view component."""
    
    def __init__(self):
        self.files: List[FileInfo] = []
        self.selected_file: Optional[str] = None
        
    def add_file(self, file_path: str) -> FileInfo:
        """Add a file to the model."""
        file_info = self._create_file_info(file_path)
        self.files.append(file_info)
        return file_info
        
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the model."""
        self.files = [f for f in self.files if f.path != file_path]
        if self.selected_file == file_path:
            self.selected_file = None
            
    def get_files(self) -> List[str]:
        """Get list of file paths."""
        return [f.path for f in self.files]
        
    def _create_file_info(self, file_path: str) -> FileInfo:
        """Create FileInfo with metadata."""
        import os
        return FileInfo(
            path=file_path,
            size=os.path.getsize(file_path),
            modified=datetime.fromtimestamp(os.path.getmtime(file_path)),
            file_type=os.path.splitext(file_path)[1],
            is_valid=True,
            is_processed=False
        )
```

### 2.2 Configuration Implementation

**Objective**: Create configuration system for file view behaviour.

**File**: `_view/center_panel_components/file_pane_v2/config.py`

**Implementation**:
```python
from dataclasses import dataclass, field
from typing import List

@dataclass
class FileConfig:
    """Configuration for file view behaviour and appearance."""
    
    # Display options
    show_file_icons: bool = True
    show_file_size: bool = True
    show_file_type: bool = True
    
    # File display options
    group_by_folder: bool = True
    sort_by: str = "name"  # "name", "size", "type", "date"
    sort_order: str = "asc"  # "asc", "desc"
    
    # File operations
    allow_add: bool = True
    allow_remove: bool = True
    allow_context_menu: bool = True
    
    # File types
    allowed_file_types: List[str] = field(
        default_factory=lambda: ["*.csv"]
    )
```

### 2.3 Internal Components Implementation

**Objective**: Implement internal UI components.

#### 2.3.1 File Table Component

**File**: `_view/center_panel_components/file_pane_v2/components/file_table.py`

**Key Features**:
- Display file list with metadata
- Handle file selection
- Emit selection signals
- Support sorting and filtering

#### 2.3.2 Add/Remove Buttons Component

**File**: `_view/center_panel_components/file_pane_v2/components/add_remove_btns.py`

**Key Features**:
- Add files button with file dialog
- Remove selected file button
- Enable/disable based on selection state
- Emit action signals

#### 2.3.3 Context Menu Component

**File**: `_view/center_panel_components/file_pane_v2/components/context_menu.py`

**Key Features**:
- Right-click context menu
- File operations (remove, properties, etc.)
- Context-sensitive options

### 2.4 Main Component Implementation

**Objective**: Implement the main file view component.

**File**: `_view/center_panel_components/file_pane_v2/ud_file_view.py`

**Key Features**:
- Inherit from BasePane
- Compose internal components
- Implement interface methods
- Publish events for asynchronous operations
- Self-contained file management

## Phase 3: Integration and Signal Handling

### 3.1 Interface Extension Implementation

**Objective**: Implement file operations in the existing interface.

**File**: `interface/i_view_interface.py`

**Actions**:
1. Add file operation methods to IUpdateDataView Protocol
2. Ensure methods are properly typed
3. Maintain backward compatibility

### 3.2 Events Integration

**Objective**: Integrate file events with existing event system.

**Files**:
- `services/events.py` (or enhanced version)
- `services/event_models.py` (renamed from events_data.py)

**Actions**:
1. Add file-specific events to existing enum
2. Create event data classes for file operations
3. Ensure compatibility with global event bus

### 3.3 View Layer Updates

**Objective**: Update main view to use new file component.

**File**: `ud_view.py`

**Actions**:
1. Import new file view component
2. Replace old file pane with new component
3. Implement interface methods that delegate to file view
4. Connect file view events to view-level signals
5. Remove signal handling from layout managers

### 3.4 Presenter Updates

**Objective**: Update presenter to use clean interface.

**File**: `ud_presenter.py`

**Actions**:
1. Connect to view-level signals (not widget signals)
2. Use interface methods for file operations
3. Remove direct Qt widget dependencies
4. Update event handlers for new event structure

## Phase 4: Migration and Testing

### 4.1 Parallel Implementation

**Objective**: Run both implementations during transition.

**Actions**:
1. Keep original file pane functional
2. Add configuration to switch between implementations
3. Test both implementations with same data
4. Compare behaviour and fix discrepancies

### 4.2 Integration Testing

**Objective**: Verify end-to-end functionality.

**Test Areas**:
- Signal flow: Widget → View → Presenter
- File operations: Add, remove, selection
- Event publishing and handling
- Interface method calls
- Configuration changes
- Error handling

### 4.3 Migration Switch

**Objective**: Switch to new implementation.

**Actions**:
1. Update center panel layout manager to use new file view
2. Remove old file pane references
3. Update any remaining imports
4. Test in production environment

### 4.4 Cleanup

**Objective**: Remove deprecated code.

**Actions**:
1. Remove old file pane implementation
2. Clean up unused imports
3. Update documentation
4. Remove temporary configuration switches
5. Final integration testing

## Success Metrics

### Phase 1 Success Criteria
- [ ] New directory structure created
- [ ] Panel files renamed with clear naming
- [ ] Events system reviewed and enhanced
- [ ] Interface extended with file operations

### Phase 2 Success Criteria
- [ ] Data model implemented and tested
- [ ] Configuration system working
- [ ] All internal components functional
- [ ] Main component integrates all parts

### Phase 3 Success Criteria
- [ ] Interface methods implemented
- [ ] Events properly integrated
- [ ] View layer updated and functional
- [ ] Presenter has zero Qt coupling

### Phase 4 Success Criteria
- [ ] Parallel implementation working
- [ ] All tests passing
- [ ] Migration completed successfully
- [ ] Old code removed and cleaned up

## Risk Mitigation

### Potential Risks
1. **Breaking existing functionality** - Mitigated by parallel implementation
2. **Signal connection issues** - Mitigated by thorough testing of signal flow
3. **Performance degradation** - Mitigated by benchmarking old vs new
4. **Integration complexity** - Mitigated by phased approach

### Rollback Plan
- Keep original implementation until new one is fully tested
- Maintain configuration switch for quick rollback
- Document all changes for easy reversal if needed
