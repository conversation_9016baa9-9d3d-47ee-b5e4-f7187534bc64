# Phase 2: Core Component Implementation

## Objective

Implement the core functionality of the file view component, including the file table, add/remove buttons, and internal logic for file management.

## Steps

### 2.1 Implement File Table Component

Create the core file table widget that will display the list of files.

**File: `ui/view/components/file_pane/components/file_table.py`**

```python
from PySide6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QHeaderView, 
    QAbstractItemView, QMenu, QAction
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon, QColor
from typing import List, Optional, Dict, Any
from ..models import FileInfo
from fm.core.services.logger import log
import os
from datetime import datetime

class FileTable(QTableWidget):
    """Table widget for displaying files."""
    
    # Internal signals
    file_selected = Signal(str)
    context_menu_requested = Signal(str, QPoint)
    
    def __init__(self, parent=None):
        """Initialize the file table."""
        super().__init__(parent)
        
        # Configure table properties
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setAlternatingRowColors(True)
        self.setShowGrid(False)
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        
        # Set up header
        self.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.horizontalHeader().setDefaultAlignment(Qt.AlignLeft)
        self.verticalHeader().setVisible(False)
        
        # Connect signals
        self.itemSelectionChanged.connect(self._on_selection_changed)
        self.customContextMenuRequested.connect(self._on_context_menu_requested)
        
        # Initialize internal state
        self._files: List[FileInfo] = []
        self._columns = ["name", "file_type", "size", "modified"]
        self._setup_columns()
        
    def _setup_columns(self):
        """Set up the table columns."""
        column_headers = {
            "name": "Filename",
            "folder": "Folder",
            "file_type": "Type",
            "size": "Size",
            "created": "Created",
            "modified": "Modified",
            "bank_type": "Bank",
            "variant": "Variant"
        }
        
        # Set column count and headers
        self.setColumnCount(len(self._columns))
        for i, col in enumerate(self._columns):
            self.setHorizontalHeaderItem(i, QTableWidgetItem(column_headers.get(col, col.title())))
            
    def set_columns(self, columns: List[str]):
        """Set the columns to display."""
        self._columns = columns
        self._setup_columns()
        self.refresh()
        
    def set_files(self, files: List[FileInfo]):
        """Set the files to display."""
        self._files = files
        self.refresh()
        
    def refresh(self):
        """Refresh the table display."""
        # Clear table
        self.setRowCount(0)
        
        # Add rows
        self.setRowCount(len(self._files))
        
        for row, file_info in enumerate(self._files):
            for col, column_name in enumerate(self._columns):
                item = QTableWidgetItem()
                
                # Set cell value based on column
                if column_name == "name":
                    item.setText(file_info.name)
                elif column_name == "folder":
                    item.setText(os.path.basename(file_info.folder))
                elif column_name == "file_type":
                    item.setText(file_info.file_type)
                elif column_name == "size":
                    item.setText(self._format_size(file_info.size))
                elif column_name == "created":
                    item.setText(self._format_date(file_info.created))
                elif column_name == "modified":
                    item.setText(self._format_date(file_info.modified))
                elif column_name == "bank_type":
                    item.setText(file_info.bank_type)
                elif column_name == "variant":
                    item.setText(file_info.variant)
                else:
                    # Try to get from metadata
                    item.setText(str(file_info.metadata.get(column_name, "")))
                
                # Set item flags
                item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
                
                # Set background color based on validity/processing
                if not file_info.is_valid:
                    item.setBackground(QColor(255, 200, 200))  # Light red
                elif file_info.is_processed:
                    item.setBackground(QColor(200, 255, 200))  # Light green
                
                # Add to table
                self.setItem(row, col, item)
                
        # Resize columns to content
        self.resizeColumnsToContents()
        
    def get_selected_file(self) -> Optional[FileInfo]:
        """Get the currently selected file."""
        selected_rows = self.selectionModel().selectedRows()
        if not selected_rows:
            return None
            
        row = selected_rows[0].row()
        if 0 <= row < len(self._files):
            return self._files[row]
            
        return None
        
    def _on_selection_changed(self):
        """Handle selection changes."""
        selected_file = self.get_selected_file()
        if selected_file:
            self.file_selected.emit(selected_file.path)
            
    def _on_context_menu_requested(self, pos):
        """Handle context menu requests."""
        selected_file = self.get_selected_file()
        if selected_file:
            self.context_menu_requested.emit(selected_file.path, pos)
            
    def _format_size(self, size_bytes: int) -> str:
        """Format file size for display."""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
            
    def _format_date(self, date: datetime) -> str:
        """Format date for display."""
        if not date:
            return ""
        return date.strftime("%Y-%m-%d %H:%M")
```

### 2.2 Implement Add/Remove Buttons Component

Create the component for add/remove file buttons.

**File: `ui/view/components/file_pane/components/add_remove_btns.py`**

```python
from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QToolButton
from PySide6.QtCore import Signal, Qt
from PySide6.QtGui import QIcon
from fm.core.services.logger import log

class AddRemoveButtons(QWidget):
    """Container for Add/Remove buttons."""
    
    # Signals
    add_clicked = Signal()
    remove_clicked = Signal()
    
    def __init__(self, parent=None):
        """Initialize the buttons container."""
        super().__init__(parent)
        
        # Set up layout
        self._layout = QHBoxLayout()
        self._layout.setContentsMargins(0, 0, 0, 0)
        self._layout.setSpacing(5)
        self.setLayout(self._layout)
        
        # Create buttons
        self._add_button = self._create_button("Add Files", "plus")
        self._remove_button = self._create_button("Remove File", "minus")
        
        # Add buttons to layout
        self._layout.addWidget(self._add_button)
        self._layout.addWidget(self._remove_button)
        self._layout.addStretch()
        
        # Connect signals
        self._add_button.clicked.connect(self.add_clicked)
        self._remove_button.clicked.connect(self.remove_clicked)
        
    def _create_button(self, text, icon_name=None):
        """Create a button with text and optional icon."""
        button = QPushButton(text)
        
        if icon_name:
            # Use icon if available
            try:
                icon = QIcon.fromTheme(icon_name)
                if not icon.isNull():
                    button.setIcon(icon)
            except Exception as e:
                log.warning(f"Failed to set icon {icon_name}: {e}")
                
        return button
        
    def set_remove_enabled(self, enabled):
        """Enable or disable the remove button."""
        self._remove_button.setEnabled(enabled)
```

### 2.3 Implement Context Menu

Create the context menu for file operations.

**File: `ui/view/components/file_pane/components/context_menu.py`**

```python
from PySide6.QtWidgets import QMenu, QAction
from PySide6.QtCore import QPoint, Signal
from typing import Optional, Dict, Any
from fm.core.services.logger import log

class FileContextMenu(QMenu):
    """Context menu for file operations."""
    
    # Signals
    remove_requested = Signal(str)
    open_requested = Signal(str)
    view_info_requested = Signal(str)
    
    def __init__(self, parent=None):
        """Initialize the context menu."""
        super().__init__(parent)
        
        # Create actions
        self._open_action = QAction("Open File", self)
        self._view_info_action = QAction("View File Info", self)
        self._remove_action = QAction("Remove from List", self)
        
        # Add actions to menu
        self.addAction(self._open_action)
        self.addAction(self._view_info_action)
        self.addSeparator()
        self.addAction(self._remove_action)
        
        # Connect signals
        self._open_action.triggered.connect(self._on_open_triggered)
        self._view_info_action.triggered.connect(self._on_view_info_triggered)
        self._remove_action.triggered.connect(self._on_remove_triggered)
        
        # Initialize state
        self._current_file: Optional[str] = None
        
    def show_for_file(self, file_path: str, position: QPoint):
        """Show the context menu for a specific file."""
        self._current_file = file_path
        self.popup(position)
        
    def _on_open_triggered(self):
        """Handle open action."""
        if self._current_file:
            self.open_requested.emit(self._current_file)
            
    def _on_view_info_triggered(self):
        """Handle view info action."""
        if self._current_file:
            self.view_info_requested.emit(self._current_file)
            
    def _on_remove_triggered(self):
        """Handle remove action."""
        if self._current_file:
            self.remove_requested.emit(self._current_file)
```

### 2.4 Implement File Info Service Integration

Create a utility file for file information services.

**File: `ui/view/components/file_pane/utils.py`**

```python
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
from .models import FileInfo
import os

def create_file_info(file_path: str) -> FileInfo:
    """Create a FileInfo object from a file path."""
    path_obj = Path(file_path)
    
    # Get basic file information
    file_info = FileInfo(path=file_path)
    
    # Get file size
    try:
        file_info.size = path_obj.stat().st_size
    except Exception:
        file_info.size = 0
        
    # Get file dates
    try:
        file_info.created = datetime.fromtimestamp(path_obj.stat().st_ctime)
        file_info.modified = datetime.fromtimestamp(path_obj.stat().st_mtime)
    except Exception:
        file_info.created = datetime.now()
        file_info.modified = datetime.now()
        
    # Get file type
    file_info.file_type = path_obj.suffix.lower().lstrip(".")
    
    # Determine if file is valid
    file_info.is_valid = path_obj.exists() and path_obj.is_file()
    
    return file_info

def enrich_file_info(file_info: FileInfo, file_info_service=None) -> FileInfo:
    """Enrich a FileInfo object with additional metadata."""
    # Use file_info_service if available
    if file_info_service:
        try:
            # Get bank-specific information
            metadata = file_info_service.get_file_info(file_info.path)
            if metadata:
                file_info.bank_type = metadata.get("bank_type", "")
                file_info.variant = metadata.get("variant", "")
                file_info.metadata.update(metadata)
        except Exception as e:
            # Log error but continue
            print(f"Error enriching file info: {e}")
            
    return file_info
```

### 2.5 Update Main Component Implementation

Update the main component with the implemented subcomponents.

**File: `ui/view/components/file_pane/ud_file_view.py`**

```python
from PySide6.QtWidgets import QVBoxLayout, QWidget, QFileDialog
from PySide6.QtCore import Qt
from fm.gui.components.shared.base_pane import BasePane
from .config import FileViewConfig
from .models import FileInfo
from .components.file_table import FileTable
from .components.add_remove_btns import AddRemoveButtons
from .components.context_menu import FileContextMenu
from .utils import create_file_info, enrich_file_info
from fm.modules.update_data.ui.events.file_events import (
    FileViewEvents, FileSelectedEvent, FileListChangedEvent
)
from typing import List, Optional, Dict, Any
from fm.core.services.logger import log
import os
from pathlib import Path

class UDFileView(BasePane):
    """Self-contained file view component."""
    
    def __init__(self, parent=None, file_info_service=None):
        """Initialize the component."""
        super().__init__(parent)
        self.setTitle("Files")
        
        # Create events object
        self.events = FileViewEvents()
        
        # Initialize services
        self._file_info_service = file_info_service
        
        # Initialize internal state
        self._files: List[FileInfo] = []
        self._selected_file: Optional[FileInfo] = None
        self._config = FileViewConfig()
        
        # Set up UI
        self._setup_ui()
        self._connect_signals()
        
    def _setup_ui(self):
        """Set up the component UI."""
        # Main layout
        self._main_layout = QVBoxLayout()
        self._main_layout.setContentsMargins(0, 0, 0, 0)
        self._main_layout.setSpacing(5)
        
        # Create components
        self._file_table = FileTable(self)
        self._add_remove_btns = AddRemoveButtons(self)
        self._context_menu = FileContextMenu(self)
        
        # Add components to layout
        self._main_layout.addWidget(self._file_table)
        self._main_layout.addWidget(self._add_remove_btns)
        
        # Set layout to content widget from BasePane
        self.content_widget.setLayout(self._main_layout)
        
    def _connect_signals(self):
        """Connect internal signals."""
        # Connect file table signals
        self._file_table.file_selected.connect(self._on_file_selected)
        self._file_table.context_menu_requested.connect(self._on_context_menu_requested)
        
        # Connect button signals
        self._add_remove_btns.add_clicked.connect(self._on_add_clicked)
        self._add_remove_btns.remove_clicked.connect(self._on_remove_clicked)
        
        # Connect context menu signals
        self._context_menu.remove_requested.connect(self.remove_file)
        self._context_menu.open_requested.connect(self._on_open_file)
        self._context_menu.view_info_requested.connect(self._on_view_file_info)
        
    # Interface methods
    
    def set_files(self, files: List[str]) -> None:
        """Set the files to display."""
        log.info(f"UDFileView: Setting {len(files)} files")
        
        # Clear existing files
        self._files = []
        
        # Add each file
        for file_path in files:
            file_info = create_file_info(file_path)
            file_info = enrich_file_info(file_info, self._file_info_service)
            self._files.append(file_info)
            
        # Update table
        self._file_table.set_files(self._files)
        
        # Publish event
        self._publish_file_list_changed()
        
    def add_file(self, file_path: str) -> None:
        """Add a single file to the view."""
        log.info(f"UDFileView: Adding file {file_path}")
        
        # Create file info
        file_info = create_file_info(file_path)
        file_info = enrich_file_info(file_info, self._file_info_service)
        
        # Add to list
        self._files.append(file_info)
        
        # Update table
        self._file_table.set_files(self._files)
        
        # Publish event
        self._publish_file_list_changed()
        
    def add_files(self, files: List[str]) -> None:
        """Add multiple files to the view."""
        log.info(f"UDFileView: Adding {len(files)} files")
        
        # Add each file
        for file_path in files:
            file_info = create_file_info(file_path)
            file_info = enrich_file_info(file_info, self._file_info_service)
            self._files.append(file_info)
            
        # Update table
        self._file_table.set_files(self._files)
        
        # Publish event
        self._publish_file_list_changed()
        
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the view."""
        log.info(f"UDFileView: Removing file {file_path}")
        
        # Remove from list
        self._files = [f for f in self._files if f.path != file_path]
        
        # Update table
        self._file_table.set_files(self._files)
        
        # Update selected file
        if self._selected_file and self._selected_file.path == file_path:
            self._selected_file = None
            
        # Update button state
        self._update_button_state()
        
        # Publish event
        self._publish_file_list_changed()
        
    def get_files(self) -> List[str]:
        """Get the current list of files."""
        return [f.path for f in self._files]
        
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file."""
        return self._selected_file.path if self._selected_file else None
        
    def clear_files(self) -> None:
        """Clear all files from the view."""
        log.info("UDFileView: Clearing all files")
        
        # Clear files
        self._files = []
        self._selected_file = None
        
        # Update table
        self._file_table.set_files(self._files)
        
        # Update button state
        self._update_button_state()
        
        # Publish event
        self._publish_file_list_changed()
        
    def set_processing_state(self, processing: bool) -> None:
        """Set the processing state of the view."""
        log.info(f"UDFileView: Setting processing state to {processing}")
        
        # Update UI state based on processing
        self.setEnabled(not processing)
        
    def configure(self, **kwargs) -> None:
        """Configure component behavior."""
        # Update configuration
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
                log.debug(f"UDFileView: Set config {key} = {value}")
        
        # Apply configuration
        self._apply_configuration()
        
        return self
        
    # Internal methods
    
    def _apply_configuration(self):
        """Apply the current configuration."""
        # Update columns
        self._file_table.set_columns(self._config.columns)
        
    def _update_button_state(self):
        """Update button states based on current selection."""
        has_selection = self._selected_file is not None
        self._add_remove_btns.set_remove_enabled(has_selection)
        
    def _publish_file_list_changed(self):
        """Publish file list changed event."""
        files = self.get_files()
        event = FileListChangedEvent(files=files)
        self.events.file_list_changed.emit(event)
        
    def _publish_file_selected(self, file_path: str):
        """Publish file selected event."""
        event = FileSelectedEvent(file_path=file_path)
        self.events.file_selected.emit(event)
        
    # Event handlers
    
    def _on_file_selected(self, file_path: str):
        """Handle file selection."""
        # Update selected file
        self._selected_file = next((f for f in self._files if f.path == file_path), None)
        
        # Update button state
        self._update_button_state()
        
        # Publish event
        self._publish_file_selected(file_path)
        
    def _on_context_menu_requested(self, file_path: str, position):
        """Handle context menu request."""
        self._context_menu.show_for_file(file_path, self._file_table.mapToGlobal(position))
        
    def _on_add_clicked(self):
        """Handle add button click."""
        # Show file dialog
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "Select Files",
            "",
            f"Supported Files ({' '.join(self._config.allowed_file_types)})"
        )
        
        if files:
            self.add_files(files)
            self.events.add_files_requested.emit()
            
    def _on_remove_clicked(self):
        """Handle remove button click."""
        if self._selected_file:
            file_path = self._selected_file.path
            self.remove_file(file_path)
            self.events.remove_file_requested.emit(file_path)
            
    def _on_open_file(self, file_path: str):
        """Handle open file request."""
        # Use system default application to open file
        try:
            import subprocess
            import os
            import sys
            
            if sys.platform == 'win32':
                os.startfile(file_path)
            elif sys.platform == 'darwin':
                subprocess.call(('open', file_path))
            else:
                subprocess.call(('xdg-open', file_path))
        except Exception as e:
            log.error(f"Failed to open file {file_path}: {e}")
            
    def _on_view_file_info(self, file_path: str):
        """Handle view file info request."""
        # TODO: Implement file info dialog in Phase 3
        log.info(f"View file info requested for {file_path}")
```

### 2.6 Create Unit Tests

Create basic unit tests for the file view component.

**File: `tests/modules/update_data/ui/view/components/file_pane/test_ud_file_view.py`**

```python
import pytest
from PySide6.QtWidgets import QApplication
from fm.modules.update_data.ui.view.components.file_pane.ud_file_view import UDFileView
from fm.modules.update_data.ui.events.file_events import FileListChangedEvent, FileSelectedEvent

# Mock file paths for testing
TEST_FILES = [
    "C:/test/file1.csv",
    "C:/test/file2.csv",
    "C:/test/file3.csv"
]

@pytest.fixture
def app():
    """Create QApplication instance."""
    return QApplication([])

@pytest.fixture
def file_view(app):
    """Create UDFileView instance."""
    return UDFileView()

def test_file_view_initialization(file_view):
    """Test file view initialization."""
    assert file_view is not None
    assert hasattr(file_view, "events")
    assert len(file_view.get_files()) == 0
    assert file_view.get_selected_file() is None

def test_add_files(file_view):
    """Test adding files."""
    # Set up event capture
    received_events = []
    file_view.events.file_list_changed.connect(
        lambda event: received_events.append(event)
    )
    
    # Add files
    file_view.add_files(TEST_FILES)
    
    # Check files were added
    assert len(file_view.get_files()) == len(TEST_FILES)
    for file_path in TEST_FILES:
        assert file_path in file_view.get_files()
        
    # Check event was emitted
    assert len(received_events) == 1
    assert isinstance(received_events[0], FileListChangedEvent)
    assert len(received_events[0].files) == len(TEST_FILES)

def test_remove_file(file_view):
    """Test removing a file."""
    # Add files
    file_view.add_files(TEST_FILES)
    
    # Set up event capture
    received_events = []
    file_view.events.file_list_changed.connect(
        lambda event: received_events.append(event)
    )
    
    # Remove a file
    file_to_remove = TEST_FILES[0]
    file_view.remove_file(file_to_remove)
    
    # Check file was removed
    assert len(file_view.get_files()) == len(TEST_FILES) - 1
    assert file_to_remove not in file_view.get_files()
    
    # Check event was emitted
    assert len(received_events) == 1
    assert isinstance(received_events[0], FileListChangedEvent)
    assert len(received_events[0].files) == len(TEST_FILES) - 1
    assert file_to_remove not in received_events[0].files

def test_clear_files(file_view):
    """Test clearing all files."""
    # Add files
    file_view.add_files(TEST_FILES)
    
    # Set up event capture
    received_events = []
    file_view.events.file_list_changed.connect(
        lambda event: received_events.append(event)
    )
    
    # Clear files
    file_view.clear_files()
    
    # Check files were cleared
    assert len(file_view.get_files()) == 0
    
    # Check event was emitted
    assert len(received_events) == 1
    assert isinstance(received_events[0], FileListChangedEvent)
    assert len(received_events[0].files) == 0

def test_configure(file_view):
    """Test configuration."""
    # Configure component
    custom_columns = ["name", "size", "modified"]
    file_view.configure(
        columns=custom_columns,
        show_file_size=False,
        allowed_file_types=["*.csv", "*.ofx"]
    )
    
    # Check configuration was applied
    assert file_view._config.columns == custom_columns
    assert file_view._config.show_file_size is False
    assert "*.csv" in file_view._config.allowed_file_types
    assert "*.ofx" in file_view._config.allowed_file_types
```

## Deliverables

1. File table component
2. Add/remove buttons component
3. Context menu component
4. File info utilities
5. Updated main component implementation
6. Unit tests

## Testing

Run the unit tests to verify:

1. Component initialization works correctly
2. File operations (add, remove, clear) work correctly
3. Events are emitted correctly
4. Configuration can be applied

## Next Steps

Proceed to [Phase 3: Integration with Update Data Module](./phase3_integration.md) to integrate the file view component with the update data module.
