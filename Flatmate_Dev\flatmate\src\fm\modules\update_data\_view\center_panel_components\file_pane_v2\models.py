"""
Data models for the file view component.

Contains data structures and view models for managing file information
and component state.
"""

from dataclasses import dataclass, field
from typing import List, Optional
from datetime import datetime
from pathlib import Path


@dataclass
class FileInfo:
    """Data structure for file information."""
    path: str
    size: int
    modified: datetime
    file_type: str
    is_valid: bool = True
    is_processed: bool = False
    
    @property
    def name(self) -> str:
        """Get the filename without path."""
        return Path(self.path).name
    
    @property
    def size_formatted(self) -> str:
        """Get human-readable file size."""
        if self.size < 1024:
            return f"{self.size} B"
        elif self.size < 1024 * 1024:
            return f"{self.size / 1024:.1f} KB"
        else:
            return f"{self.size / (1024 * 1024):.1f} MB"


class FileViewModel:
    """Data model for the file view component."""
    
    def __init__(self):
        self.files: List[FileInfo] = []
        self.selected_file: Optional[str] = None
        self._sort_by: str = "name"
        self._sort_order: str = "asc"
    
    def add_file(self, file_info: FileInfo) -> None:
        """Add a file to the model."""
        # Check if file already exists
        existing_paths = [f.path for f in self.files]
        if file_info.path not in existing_paths:
            self.files.append(file_info)
            self._sort_files()
    
    def remove_file(self, file_path: str) -> bool:
        """Remove a file from the model. Returns True if removed."""
        for i, file_info in enumerate(self.files):
            if file_info.path == file_path:
                del self.files[i]
                if self.selected_file == file_path:
                    self.selected_file = None
                return True
        return False
    
    def get_file_paths(self) -> List[str]:
        """Get list of all file paths."""
        return [f.path for f in self.files]
    
    def set_selected_file(self, file_path: Optional[str]) -> None:
        """Set the selected file."""
        if file_path is None or file_path in self.get_file_paths():
            self.selected_file = file_path
    
    def clear_files(self) -> None:
        """Clear all files from the model."""
        self.files.clear()
        self.selected_file = None
    
    def set_sort_criteria(self, sort_by: str, sort_order: str = "asc") -> None:
        """Set sorting criteria and re-sort files."""
        self._sort_by = sort_by
        self._sort_order = sort_order
        self._sort_files()
    
    def _sort_files(self) -> None:
        """Sort files based on current criteria."""
        reverse = self._sort_order == "desc"
        
        if self._sort_by == "name":
            self.files.sort(key=lambda f: f.name.lower(), reverse=reverse)
        elif self._sort_by == "size":
            self.files.sort(key=lambda f: f.size, reverse=reverse)
        elif self._sort_by == "type":
            self.files.sort(key=lambda f: f.file_type, reverse=reverse)
        elif self._sort_by == "date":
            self.files.sort(key=lambda f: f.modified, reverse=reverse)
