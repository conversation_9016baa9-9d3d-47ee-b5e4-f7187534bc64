"""
Context menu for file operations.

Internal component for the file view widget.
"""

from PySide6.QtWidgets import QMenu
from PySide6.QtCore import Signal, QObject
from PySide6.QtGui import QAction
from typing import Optional
from ..config import FileConfig
from fm.core.services.logger import log


class FileContextMenu(QObject):
    """Context menu for file operations."""
    
    # Signals
    remove_file_requested = Signal(str)  # file_path
    open_file_location_requested = Signal(str)  # file_path
    file_properties_requested = Signal(str)  # file_path
    
    def __init__(self, config: FileConfig, parent=None):
        super().__init__(parent)
        self._config = config
        self._current_file_path: Optional[str] = None
    
    def create_menu(self, file_path: Optional[str] = None) -> QMenu:
        """Create and return a context menu for the given file."""
        if not self._config.allow_context_menu:
            return QMenu()
        
        self._current_file_path = file_path
        menu = QMenu()
        
        if file_path:
            # File-specific actions
            self._add_file_actions(menu, file_path)
        else:
            # General actions when no file is selected
            self._add_general_actions(menu)
        
        return menu
    
    def _add_file_actions(self, menu: QMenu, file_path: str) -> None:
        """Add file-specific actions to the menu."""
        # Remove file action
        if self._config.allow_remove:
            remove_action = QAction("Remove from list", menu)
            remove_action.setToolTip("Remove this file from the list")
            remove_action.triggered.connect(
                lambda: self._on_remove_file_triggered(file_path)
            )
            menu.addAction(remove_action)
        
        menu.addSeparator()
        
        # Open file location action
        open_location_action = QAction("Open file location", menu)
        open_location_action.setToolTip("Open the folder containing this file")
        open_location_action.triggered.connect(
            lambda: self._on_open_location_triggered(file_path)
        )
        menu.addAction(open_location_action)
        
        # File properties action
        properties_action = QAction("Properties", menu)
        properties_action.setToolTip("View file properties")
        properties_action.triggered.connect(
            lambda: self._on_properties_triggered(file_path)
        )
        menu.addAction(properties_action)
    
    def _add_general_actions(self, menu: QMenu) -> None:
        """Add general actions when no file is selected."""
        if self._config.allow_add:
            add_action = QAction("Add files...", menu)
            add_action.setToolTip("Add files to the list")
            # Note: This would need to be connected to the parent widget's add functionality
            menu.addAction(add_action)
    
    def _on_remove_file_triggered(self, file_path: str) -> None:
        """Handle remove file action."""
        log.debug(f"Context menu: Remove file requested for {file_path}")
        self.remove_file_requested.emit(file_path)
    
    def _on_open_location_triggered(self, file_path: str) -> None:
        """Handle open file location action."""
        log.debug(f"Context menu: Open location requested for {file_path}")
        self.open_file_location_requested.emit(file_path)
    
    def _on_properties_triggered(self, file_path: str) -> None:
        """Handle file properties action."""
        log.debug(f"Context menu: Properties requested for {file_path}")
        self.file_properties_requested.emit(file_path)
