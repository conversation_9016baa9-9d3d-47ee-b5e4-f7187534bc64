"""
View components for the Update Data module.

This package provides a modular, component-based UI for the Update Data module.
Components are organized into the following categories:

- _common: Base components and core structural elements
- center_panel: Center panel components for main content display
- left_panel: Left panel components for navigation
- right_panel: Right panel components for settings and options
- utils: Helper utilities for the view components
"""

# Import panel managers normally - circular imports resolved
from .center_panel import CenterPanelManager
from .left_panel_layout import LeftPanelManager
from .right_panel_layout import RightPanelManager

__all__ = [
    'CenterPanelManager',
    'LeftPanelManager',
    'RightPanelManager',
]
