# Phase 3: Integration with Update Data Module

## Objective

Integrate the self-contained file view component with the update data module's view layer and presenter, ensuring proper signal connections and event handling.

## Steps

### 3.1 Update View Interface

Update the view interface to include the file view methods.

**File: `ui/view/interface/i_ud_view.py`**

```python
from typing import Protocol, List, Optional

class IUpdateDataView(Protocol):
    """Interface for update data view - METHODS ONLY."""
    
    # File view methods
    def set_files(self, files: List[str]) -> None:
        """Set the files to display."""
        ...
        
    def add_file(self, file_path: str) -> None:
        """Add a single file to the view."""
        ...
        
    def add_files(self, files: List[str]) -> None:
        """Add multiple files to the view."""
        ...
        
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the view."""
        ...
        
    def get_files(self) -> List[str]:
        """Get the current list of files."""
        ...
        
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file."""
        ...
        
    def clear_files(self) -> None:
        """Clear all files from the view."""
        ...
        
    def set_file_processing_state(self, processing: bool) -> None:
        """Set the processing state of the file view."""
        ...
        
    def configure_file_view(self, **kwargs) -> None:
        """Configure file view behavior."""
        ...
        
    # Other view methods would be here...
```

### 3.2 Update View Events

Update the view events class to include file-related events.

**File: `ui/events/ud_view_events.py`**

```python
from PySide6.QtCore import Signal, QObject
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

@dataclass
class FileSelectedEvent:
    """Event data for file selection."""
    file_path: str
    timestamp: datetime = datetime.now()

@dataclass
class FileListChangedEvent:
    """Event data for file list changes."""
    files: List[str]
    source_path: str = ""
    timestamp: datetime = datetime.now()

class UpdateDataViewEvents(QObject):
    """Events published by update data view."""
    
    # File selection events
    file_selected = Signal(object)  # FileSelectedEvent
    
    # File list events
    file_list_changed = Signal(object)  # FileListChangedEvent
    
    # User action events
    add_files_requested = Signal()
    remove_file_requested = Signal(str)
    
    # Other view events would be here...
```

### 3.3 Update View Implementation

Update the view implementation to include the file view component.

**File: `ui/view/ud_view.py`**

```python
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout
from fm.modules.update_data.ui.view.interface.i_ud_view import IUpdateDataView
from fm.modules.update_data.ui.view.components.file_pane.ud_file_view import UDFileView
from fm.modules.update_data.ui.events.ud_view_events import UpdateDataViewEvents
from fm.modules.update_data.ui.events.file_events import FileSelectedEvent, FileListChangedEvent
from typing import List, Optional
from fm.core.services.logger import log

class UpdateDataView(QWidget):
    """Main view for update data module."""
    
    def __init__(self, parent=None, file_info_service=None):
        """Initialize the view."""
        super().__init__(parent)
        
        # Create events object
        self.events = UpdateDataViewEvents()
        
        # Initialize services
        self._file_info_service = file_info_service
        
        # Set up UI
        self._setup_ui()
        self._connect_signals()
        
    def _setup_ui(self):
        """Set up the view UI."""
        # Main layout
        self._main_layout = QVBoxLayout()
        self._main_layout.setContentsMargins(10, 10, 10, 10)
        self._main_layout.setSpacing(10)
        self.setLayout(self._main_layout)
        
        # Create components
        self._file_view = UDFileView(self, self._file_info_service)
        
        # Add components to layout
        self._main_layout.addWidget(self._file_view)
        
        # Other UI components would be added here...
        
    def _connect_signals(self):
        """Connect internal signals."""
        # Connect file view events
        self._file_view.events.file_selected.connect(self._on_file_selected)
        self._file_view.events.file_list_changed.connect(self._on_file_list_changed)
        self._file_view.events.add_files_requested.connect(self._on_add_files_requested)
        self._file_view.events.remove_file_requested.connect(self._on_remove_file_requested)
        
    # File view interface methods
    
    def set_files(self, files: List[str]) -> None:
        """Set the files to display."""
        self._file_view.set_files(files)
        
    def add_file(self, file_path: str) -> None:
        """Add a single file to the view."""
        self._file_view.add_file(file_path)
        
    def add_files(self, files: List[str]) -> None:
        """Add multiple files to the view."""
        self._file_view.add_files(files)
        
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the view."""
        self._file_view.remove_file(file_path)
        
    def get_files(self) -> List[str]:
        """Get the current list of files."""
        return self._file_view.get_files()
        
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file."""
        return self._file_view.get_selected_file()
        
    def clear_files(self) -> None:
        """Clear all files from the view."""
        self._file_view.clear_files()
        
    def set_file_processing_state(self, processing: bool) -> None:
        """Set the processing state of the file view."""
        self._file_view.set_processing_state(processing)
        
    def configure_file_view(self, **kwargs) -> None:
        """Configure file view behavior."""
        self._file_view.configure(**kwargs)
        
    # Event handlers
    
    def _on_file_selected(self, event: FileSelectedEvent):
        """Handle file selection."""
        # Forward event to view events
        self.events.file_selected.emit(event)
        
    def _on_file_list_changed(self, event: FileListChangedEvent):
        """Handle file list changes."""
        # Forward event to view events
        self.events.file_list_changed.emit(event)
        
    def _on_add_files_requested(self):
        """Handle add files request."""
        # Forward event to view events
        self.events.add_files_requested.emit()
        
    def _on_remove_file_requested(self, file_path: str):
        """Handle remove file request."""
        # Forward event to view events
        self.events.remove_file_requested.emit(file_path)
```

### 3.4 Update Presenter

Update the presenter to connect to the view events and implement file handling.

**File: `ud_presenter.py`**

```python
from fm.modules.update_data.ui.view.interface.i_ud_view import IUpdateDataView
from fm.modules.update_data.ui.events.ud_view_events import (
    FileSelectedEvent, FileListChangedEvent
)
from fm.core.services.logger import log
from typing import List, Optional

class UpdateDataPresenter:
    """Presenter for update data module."""
    
    def __init__(self, view: IUpdateDataView, file_info_service=None):
        """Initialize the presenter."""
        self._view = view
        self._file_info_service = file_info_service
        
        # Initialize state
        self._files: List[str] = []
        self._selected_file: Optional[str] = None
        
        # Connect to view events
        self._connect_view_events()
        
        # Configure view
        self._configure_view()
        
    def _connect_view_events(self):
        """Connect to view events."""
        # Connect file events
        self._view.events.file_selected.connect(self._on_file_selected)
        self._view.events.file_list_changed.connect(self._on_file_list_changed)
        self._view.events.add_files_requested.connect(self._on_add_files_requested)
        self._view.events.remove_file_requested.connect(self._on_remove_file_requested)
        
    def _configure_view(self):
        """Configure the view."""
        # Configure file view
        self._view.configure_file_view(
            columns=["name", "file_type", "size", "modified"],
            allowed_file_types=["*.csv", "*.ofx", "*.qfx"]
        )
        
    # Public methods
    
    def set_files(self, files: List[str]) -> None:
        """Set the files to display."""
        self._files = files
        self._view.set_files(files)
        
    def add_file(self, file_path: str) -> None:
        """Add a single file to the view."""
        if file_path not in self._files:
            self._files.append(file_path)
            self._view.add_file(file_path)
            
    def add_files(self, files: List[str]) -> None:
        """Add multiple files to the view."""
        new_files = [f for f in files if f not in self._files]
        if new_files:
            self._files.extend(new_files)
            self._view.add_files(new_files)
            
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the view."""
        if file_path in self._files:
            self._files.remove(file_path)
            self._view.remove_file(file_path)
            
            # Update selected file if needed
            if self._selected_file == file_path:
                self._selected_file = None
                
    def get_files(self) -> List[str]:
        """Get the current list of files."""
        return self._files
        
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file."""
        return self._selected_file
        
    def clear_files(self) -> None:
        """Clear all files from the view."""
        self._files = []
        self._selected_file = None
        self._view.clear_files()
        
    # Event handlers
    
    def _on_file_selected(self, event: FileSelectedEvent):
        """Handle file selection."""
        self._selected_file = event.file_path
        log.info(f"File selected: {event.file_path}")
        
    def _on_file_list_changed(self, event: FileListChangedEvent):
        """Handle file list changes."""
        self._files = event.files
        log.info(f"File list changed: {len(event.files)} files")
        
    def _on_add_files_requested(self):
        """Handle add files request."""
        log.info("Add files requested")
        # No action needed, file view handles dialog
        
    def _on_remove_file_requested(self, file_path: str):
        """Handle remove file request."""
        log.info(f"Remove file requested: {file_path}")
        # No action needed, file view handles removal
```

### 3.5 Create Integration Tests

Create integration tests to verify the file view component works correctly with the update data module.

**File: `tests/modules/update_data/test_file_view_integration.py`**

```python
import pytest
from PySide6.QtWidgets import QApplication
from fm.modules.update_data.ui.view.ud_view import UpdateDataView
from fm.modules.update_data.ud_presenter import UpdateDataPresenter

# Mock file paths for testing
TEST_FILES = [
    "C:/test/file1.csv",
    "C:/test/file2.csv",
    "C:/test/file3.csv"
]

@pytest.fixture
def app():
    """Create QApplication instance."""
    return QApplication([])

@pytest.fixture
def view(app):
    """Create UpdateDataView instance."""
    return UpdateDataView()

@pytest.fixture
def presenter(view):
    """Create UpdateDataPresenter instance."""
    return UpdateDataPresenter(view)

def test_presenter_view_integration(presenter, view):
    """Test presenter-view integration."""
    # Add files via presenter
    presenter.add_files(TEST_FILES)
    
    # Check files in presenter
    assert len(presenter.get_files()) == len(TEST_FILES)
    for file_path in TEST_FILES:
        assert file_path in presenter.get_files()
        
    # Check files in view
    assert len(view.get_files()) == len(TEST_FILES)
    for file_path in TEST_FILES:
        assert file_path in view.get_files()

def test_view_presenter_event_flow(presenter, view):
    """Test event flow from view to presenter."""
    # Set up file list
    presenter.add_files(TEST_FILES)
    
    # Simulate file selection
    file_to_select = TEST_FILES[0]
    view._file_view._on_file_selected(file_to_select)
    
    # Check presenter state
    assert presenter.get_selected_file() == file_to_select

def test_remove_file_flow(presenter, view):
    """Test file removal flow."""
    # Set up file list
    presenter.add_files(TEST_FILES)
    
    # Remove a file via presenter
    file_to_remove = TEST_FILES[0]
    presenter.remove_file(file_to_remove)
    
    # Check presenter state
    assert file_to_remove not in presenter.get_files()
    assert len(presenter.get_files()) == len(TEST_FILES) - 1
    
    # Check view state
    assert file_to_remove not in view.get_files()
    assert len(view.get_files()) == len(TEST_FILES) - 1
```

### 3.6 Create Factory Method for File View

Create a factory method to make it easy to instantiate the file view component.

**File: `ui/view/components/file_pane/factory.py`**

```python
from .ud_file_view import UDFileView
from typing import Optional, Any

def create_file_view(
    parent=None,
    file_info_service=None,
    config: Optional[dict] = None
) -> UDFileView:
    """Create a file view component with optional configuration."""
    file_view = UDFileView(parent, file_info_service)
    
    if config:
        file_view.configure(**config)
        
    return file_view
```

## Deliverables

1. Updated view interface
2. Updated view events
3. Updated view implementation
4. Updated presenter
5. Integration tests
6. Factory method for file view

## Testing

Run the integration tests to verify:

1. Presenter-view integration works correctly
2. Event flow from view to presenter works correctly
3. File operations work end-to-end

## Next Steps

Proceed to [Phase 4: Migration and Cleanup](./phase4_migration_cleanup.md) to migrate existing functionality and clean up deprecated code.
