"""
Left panel manager for the Update Data module.
Manages the action and navigation panel with common operations.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import Q<PERSON>abel, QVBoxLayout

# Temporarily comment out to break circular import
from fm.gui._shared_components import BasePanelComponent
from fm.gui._shared_components.widgets import HeadingLabel
from .left_panel_components.widgets.widgets import (
    SourceOptionsGroup,
    ArchiveOptionsGroup,
    DatabaseCheckbox,
    ProcessActionButton,
    CancelExitButton
)


class LeftPanelManager(BasePanelComponent):
    """Manager for the left panel containing navigation and action buttons."""

    # Signals that the view expects (NO MORE buttons_widget!)
    source_select_requested = Signal(str)  # "folder" or "files"
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)  # archive_option_changed renamed
    process_clicked = Signal()
    cancel_clicked = Signal()
    update_database_changed = Signal(bool)

    # Legacy signals for compatibility
    publish_welcome_selected = Signal() # should possibly be events, these may be speculative and unimplemented )
    publish_file_selected = Signal()
    publish_data_selected = Signal()
    publish_exit_selected = Signal()
    
    def __init__(self, parent=None):
        """Initialize the left panel manager."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Panel title
        self.title = HeadingLabel("Update Data")
        layout.addWidget(self.title)

        # Create individual widgets using the widget definitions
        self.source_options_group = SourceOptionsGroup(self)
        layout.addWidget(self.source_options_group)

        self.archive_options_group = ArchiveOptionsGroup(self)
        layout.addWidget(self.archive_options_group)

        self.database_checkbox = DatabaseCheckbox(self)
        layout.addWidget(self.database_checkbox)

        self.action_button = ProcessActionButton(self)
        layout.addWidget(self.action_button)

        # Spacer
        layout.addStretch()

        self.exit_button = CancelExitButton(self)
        layout.addWidget(self.exit_button)
    
    def _connect_signals(self):
        """Connect widget signals to panel manager signals (NO MORE buttons_widget!)."""
        # Source options group
        self.source_options_group.option_changed.connect(self.source_option_changed.emit)
        self.source_options_group.button_clicked.connect(
            lambda: self.source_select_requested.emit(self.source_options_group.get_selected_option())
        )

        # Archive options group
        self.archive_options_group.option_changed.connect(self.save_option_changed.emit)
        self.archive_options_group.button_clicked.connect(self.save_select_requested.emit)

        # Action and exit buttons
        self.action_button.clicked.connect(self.process_clicked.emit)
        self.exit_button.clicked.connect(self.cancel_clicked.emit)

        # Database checkbox
        self.database_checkbox.state_changed.connect(self.update_database_changed.emit)

        # Legacy signal connections for compatibility
        self.source_options_group.button_clicked.connect(self.publish_file_selected.emit)
        self.action_button.clicked.connect(self.publish_data_selected.emit)
        self.exit_button.clicked.connect(self.publish_exit_selected.emit)
    
    # Signal handlers removed - now connecting directly to emit signals
    
    def show_component(self):
        """Show the left panel."""
        self.setVisible(True)
    
    def hide_component(self):
        """Hide the left panel."""
        self.setVisible(False)
    
    def set_process_mode(self):
        """Configure panel for process mode."""
        self.action_button.setText("Process Files")
        self.action_button.setEnabled(True)

    def set_process_button_text(self, text: str):
        """Set process button text."""
        self.action_button.setText(text)

    def set_process_button_enabled(self, enabled: bool):
        """Enable/disable process button."""
        self.action_button.setEnabled(enabled)

    def set_archive_enabled(self, enabled: bool):
        """Enable/disable archive selection."""
        self.archive_options_group.set_button_enabled(enabled)

    def get_source_option(self) -> str:
        """Get selected source option."""
        return self.source_options_group.get_selected_option()

    def get_archive_option(self) -> str:
        """Get selected archive option."""
        return self.archive_options_group.get_selected_option()

    def get_update_database(self) -> bool:
        """Get database update checkbox state."""
        return self.database_checkbox.is_checked()

    def set_source_option(self, option: str):
        """Set source option selection."""
        self.source_options_group.set_selected_option(option)
        self.exit_button.setText("Cancel")
        self.exit_button.setVisible(True)
        self.action_button.setEnabled(True)

    def set_exit_mode(self):
        """
        Configure panel for exit mode (after displaying data).

        ARCHITECTURE NOTE: Mode logic moved from widget to panel manager.
        Panel manager coordinates widget states, widgets only handle UI elements.
        """
        # Configure button states for exit mode (NO MORE buttons_widget!)
        self.action_button.setText("View Results")
        self.exit_button.setText("Exit")
        self.exit_button.setVisible(True)
        self.action_button.setEnabled(True)
